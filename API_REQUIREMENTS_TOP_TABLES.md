# API Requirements cho Top Tables Section

## Tổng quan
Tài liệu này mô tả các API endpoints cần thiết để hỗ trợ giao diện Top Tables Section trong trang Overview, bao gồm 3 bảng: TOP NẠP, TOP gói nạp phổ biến, v<PERSON> L<PERSON>ch sử nạp gần đây.

## 1. API Top Recharge (TOP NẠP)

### Endpoint
```
GET /api/statistics/top-recharge
```

### Parameters (Query)
- `limit` (optional): Số lượng record trả về (default: 10)
- `time_period` (optional): <PERSON><PERSON><PERSON><PERSON> thờ<PERSON> gian (today, yesterday, last_7_days, last_30_days, this_month, last_month)

### Response Format
```json
{
  "success": true,
  "message": "Lấy dữ liệu top nạp thành công",
  "data": [
    {
      "id": 1,
      "accountName": "player001",
      "phone": "**********",
      "totalAmount": 5000000,
      "userId": 123,
      "lastRechargeDate": "2024-08-14T10:30:00Z"
    },
    {
      "id": 2,
      "accountName": "gamer123",
      "phone": "**********",
      "totalAmount": 3500000,
      "userId": 456,
      "lastRechargeDate": "2024-08-13T15:45:00Z"
    }
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "totalPages": 10
  }
}
```

### Mô tả fields
- `id`: ID duy nhất của record
- `accountName`: Tên tài khoản game
- `phone`: Số điện thoại đăng ký
- `totalAmount`: Tổng số tiền đã nạp (VND)
- `userId`: ID người dùng trong hệ thống
- `lastRechargeDate`: Thời gian nạp gần nhất

## 2. API Top Packages (TOP gói nạp phổ biến)

### Endpoint
```
GET /api/statistics/top-packages
```

### Parameters (Query)
- `limit` (optional): Số lượng record trả về (default: 10)
- `time_period` (optional): Khoảng thời gian
- `category` (optional): Lọc theo phân loại (gold, xu, item)

### Response Format
```json
{
  "success": true,
  "message": "Lấy dữ liệu top gói nạp thành công",
  "data": [
    {
      "id": 1,
      "category": "Vàng",
      "denomination": 100000,
      "purchaseCount": 245,
      "revenue": ********,
      "packageName": "Gói Vàng 100K",
      "percentage": 15.5
    },
    {
      "id": 2,
      "category": "Vàng",
      "denomination": 200000,
      "purchaseCount": 189,
      "revenue": 37800000,
      "packageName": "Gói Vàng 200K",
      "percentage": 12.8
    }
  ],
  "summary": {
    "totalPackages": 50,
    "totalRevenue": 150000000,
    "totalPurchases": 1500
  }
}
```

### Mô tả fields
- `id`: ID gói nạp
- `category`: Phân loại (Vàng, Xu, Item, etc.)
- `denomination`: Mệnh giá (VND)
- `purchaseCount`: Số lần mua
- `revenue`: Tổng doanh thu từ gói này
- `packageName`: Tên gói nạp
- `percentage`: Phần trăm so với tổng doanh thu

## 3. API Recent History (Lịch sử nạp gần đây)

### Endpoint
```
GET /api/statistics/recent-history
```

### Parameters (Query)
- `limit` (optional): Số lượng record trả về (default: 10)
- `server` (optional): Lọc theo server
- `account` (optional): Lọc theo tài khoản

### Response Format
```json
{
  "success": true,
  "message": "Lấy lịch sử nạp gần đây thành công",
  "data": [
    {
      "id": 1,
      "account": "player001",
      "server": "S1",
      "denomination": 100000,
      "transactionId": "TXN_20240814_001",
      "createdAt": "2024-08-14T10:30:00Z",
      "status": "success",
      "paymentMethod": "banking"
    },
    {
      "id": 2,
      "account": "gamer123",
      "server": "S2",
      "denomination": 200000,
      "transactionId": "TXN_20240814_002",
      "createdAt": "2024-08-14T09:15:00Z",
      "status": "success",
      "paymentMethod": "momo"
    }
  ],
  "pagination": {
    "total": 500,
    "page": 1,
    "limit": 10,
    "totalPages": 50
  }
}
```

### Mô tả fields
- `id`: ID giao dịch
- `account`: Tên tài khoản game
- `server`: Server game (S1, S2, S3, etc.)
- `denomination`: Mệnh giá nạp (VND)
- `transactionId`: Mã giao dịch duy nhất
- `createdAt`: Thời gian tạo giao dịch
- `status`: Trạng thái (success, pending, failed)
- `paymentMethod`: Phương thức thanh toán

## Error Response Format

Tất cả API đều sử dụng format lỗi chung:

```json
{
  "success": false,
  "message": "Mô tả lỗi",
  "error": {
    "code": "ERROR_CODE",
    "details": "Chi tiết lỗi"
  }
}
```

## HTTP Status Codes
- `200`: Thành công
- `400`: Bad Request (tham số không hợp lệ)
- `401`: Unauthorized (chưa đăng nhập)
- `403`: Forbidden (không có quyền)
- `500`: Internal Server Error

## Authentication
Tất cả API đều yêu cầu authentication header:
```
Authorization: Bearer <access_token>
```

## Sample Frontend Integration

### Component Usage
```javascript
// Trong TopTablesSection.vue
import overviewApi from '@/utils/apis/overview.api.js'

// Load data với error handling
const loadTopRechargeData = async () => {
  try {
    isLoadingTopRecharge.value = true
    const result = await overviewApi.getTopRecharge({ limit: 5 })
    if (result.success && result.data) {
      topRechargeData.value = result.data
    }
  } catch (error) {
    console.error('Error loading top recharge data:', error)
    ElMessage.error('Không thể tải dữ liệu top nạp')
  } finally {
    isLoadingTopRecharge.value = false
  }
}
```

## Notes
1. Tất cả thời gian đều sử dụng format ISO 8601 UTC
2. Số tiền đều tính bằng VND (đơn vị nhỏ nhất)
3. Pagination sử dụng page-based (bắt đầu từ 1)
4. Tất cả API đều yêu cầu authentication header
5. Response data được sắp xếp theo thứ tự giảm dần (cao nhất trước)
6. Frontend component đã được tích hợp sẵn loading states và error handling
7. Mock data được sử dụng khi API chưa sẵn sàng

<template>
  <div class="top-tables-section">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Bảng TOP NẠP -->
      <el-card shadow="hover" class="table-card">
        <template #header>
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-800">TOP NẠP</h3>
          </div>
        </template>
        <el-table
          :data="topRechargeData"
          style="width: 100%"
          size="small"
          :show-header="true"
          class="custom-table"
          v-loading="isLoadingTopRecharge"
          element-loading-text="Đang tải..."
        >
          <el-table-column prop="accountName" label="Tên tài khoản" min-width="120" />
          <el-table-column prop="totalAmount" label="Tổng tiền nạp" min-width="120">
            <template #default="scope">
              <span class="font-semibold text-green-600">{{ formatCurrency(scope.row.totalAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="Hành động" min-width="120" fixed="right" align="center">
            <template #default="scope">
              <div class="action-buttons">
                <ButtonCommon
                  type="info"
                  size="small"
                  :icon="InfoIcon"
                  tooltip="Kiểm tra thông tin tài khoản"
                  @click="handleAction('check-info', scope.row)"
                />
                <ButtonCommon
                  type="primary"
                  size="small"
                  :icon="HistoryIcon"
                  tooltip="Xem lịch sử nạp tiền"
                  @click="handleAction('view-history', scope.row)"
                />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- Bảng TOP gói nạp phổ biến -->
      <el-card shadow="hover" class="table-card">
        <template #header>
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-800">TOP gói nạp phổ biến</h3>
          </div>
        </template>
        <el-table
          :data="topPackagesData"
          style="width: 100%"
          size="small"
          :show-header="true"
          class="custom-table"
          v-loading="isLoadingTopPackages"
          element-loading-text="Đang tải..."
        >
          <el-table-column prop="category" label="Phân loại" min-width="100" />
          <el-table-column prop="denomination" label="Mệnh giá" min-width="100">
            <template #default="scope">
              <span class="font-semibold text-blue-600">{{ formatCurrency(scope.row.denomination) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="purchaseCount" label="Số lần mua" min-width="100" align="center">
            <template #default="scope">
              <span class="font-semibold">{{ scope.row.purchaseCount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="revenue" label="Doanh thu" min-width="120">
            <template #default="scope">
              <span class="font-semibold text-green-600">{{ formatCurrency(scope.row.revenue) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- Bảng Lịch sử nạp gần đây -->
      <el-card shadow="hover" class="table-card">
        <template #header>
          <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-800">Lịch sử nạp gần đây</h3>
          </div>
        </template>
        <el-table
          :data="recentHistoryData"
          style="width: 100%"
          size="small"
          :show-header="true"
          class="custom-table"
          v-loading="isLoadingRecentHistory"
          element-loading-text="Đang tải..."
        >
          <el-table-column type="index" label="STT" width="50" />
          <el-table-column prop="account" label="Tài khoản" min-width="100" />
          <el-table-column prop="server" label="Server" min-width="80" />
          <el-table-column prop="denomination" label="Mệnh giá" min-width="100">
            <template #default="scope">
              <span class="font-semibold text-purple-600">{{ formatCurrency(scope.row.denomination) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled, Clock } from '@element-plus/icons-vue'
import ButtonCommon from '@/components/common/ButtonCommon.vue'
// import overviewApi from '@/utils/apis/overview.api.js' // Sẽ sử dụng khi có real API

// Icons for actions
const InfoIcon = InfoFilled
const HistoryIcon = Clock

// Loading states
const isLoadingTopRecharge = ref(false)
const isLoadingTopPackages = ref(false)
const isLoadingRecentHistory = ref(false)

// Mock data - sẽ được thay thế bằng API calls
const topRechargeData = ref([
  {
    id: 1,
    accountName: 'tranquyet',
    phone: '**********',
    totalAmount: 5000000
  },
  {
    id: 2,
    accountName: 'vuduy',
    phone: '**********',
    totalAmount: 3500000
  },
  {
    id: 3,
    accountName: 'phanthuy',
    phone: '**********',
    totalAmount: 2800000
  },
  {
    id: 4,
    accountName: 'tranphuong',
    phone: '**********',
    totalAmount: 2200000
  },
  {
    id: 5,
    accountName: 'tranquyet',
    phone: '**********',
    totalAmount: 1900000
  }
])

const topPackagesData = ref([
  {
    id: 1,
    category: 'Gói 6',
    denomination: 1000000,
    purchaseCount: 98,
    revenue: ********
  },
  {
    id: 2,
    category: 'Gói 5',
    denomination: 500000,
    purchaseCount: 245,
    revenue: *********
  },
  {
    id: 3,
    category: 'Gói 4',
    denomination: 200000,
    purchaseCount: 189,
    revenue: ********
  },
  {
    id: 4,
    category: 'Gói 3',
    denomination: 100000,
    purchaseCount: 156,
    revenue: ********
  },
  {
    id: 5,
    category: 'Gói 2',
    denomination: 50000,
    purchaseCount: 134,
    revenue: 6700000
  },
  {
    id: 6,
    category: 'Gói 1',
    denomination: 20000,
    purchaseCount: 87,
    revenue: 1740000
  },
  {
    id: 7,
    category: 'Thẻ Mcoin',
    denomination: 50000,
    purchaseCount: 76,
    revenue: 3800000
  }
])

const recentHistoryData = ref([
  {
    id: 1,
    account: 'tranquyet',
    server: 'Hồi Ức',
    denomination: 1000000
  },
  {
    id: 2,
    account: 'vuduy',
    server: 'VLV',
    denomination: 500000
  },
  {
    id: 3,
    account: 'phanthuy',
    server: 'G4VN',
    denomination: 200000
  },
  {
    id: 4,
    account: 'tranphuong',
    server: 'Kiếm Hiệp Tình',
    denomination: 100000
  },
  {
    id: 5,
    account: 'vuduy',
    server: 'Hồi Ức',
    denomination: 50000
  },
  {
    id: 6,
    account: 'tranquyet',
    server: 'VLV',
    denomination: 20000
  },
  {
    id: 7,
    account: 'phanthuy',
    server: 'G4VN',
    denomination: 100000
  }
])

// Format currency
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Action handlers
const handleAction = (command, row) => {
  switch (command) {
    case 'check-info':
      ElMessage.info(`Kiểm tra thông tin tài khoản: ${row.accountName}`)
      // TODO: Implement check info functionality
      break
    case 'view-history':
      ElMessage.info(`Xem lịch sử nạp của: ${row.accountName}`)
      // TODO: Implement view history functionality
      break
    default:
      break
  }
}

// Load data functions - Sử dụng mock data
const loadTopRechargeData = async () => {
  try {
    isLoadingTopRecharge.value = true
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500))
    // Mock data đã được set sẵn, không cần gọi API
    console.log('Top recharge data loaded (mock)')
  } catch (error) {
    console.error('Error loading top recharge data:', error)
    ElMessage.error('Không thể tải dữ liệu top nạp')
  } finally {
    isLoadingTopRecharge.value = false
  }
}

const loadTopPackagesData = async () => {
  try {
    isLoadingTopPackages.value = true
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 600))
    // Mock data đã được set sẵn, không cần gọi API
    console.log('Top packages data loaded (mock)')
  } catch (error) {
    console.error('Error loading top packages data:', error)
    ElMessage.error('Không thể tải dữ liệu top gói nạp')
  } finally {
    isLoadingTopPackages.value = false
  }
}

const loadRecentHistoryData = async () => {
  try {
    isLoadingRecentHistory.value = true
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 400))
    // Mock data đã được set sẵn, không cần gọi API
    console.log('Recent history data loaded (mock)')
  } catch (error) {
    console.error('Error loading recent history data:', error)
    ElMessage.error('Không thể tải dữ liệu lịch sử nạp')
  } finally {
    isLoadingRecentHistory.value = false
  }
}

onMounted(() => {
  // Load all data when component mounts
  loadTopRechargeData()
  loadTopPackagesData()
  loadRecentHistoryData()
})
</script>

<style lang="scss" scoped>
.top-tables-section {
  margin-top: 2rem;
}

.table-card {
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  background-color: white;

  .dark & {
    background-color: #1f2937;
    border-color: #374151;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    color: #1f2937;

    .dark & {
      color: #f9fafb;
    }
  }
}

.custom-table {
  :deep(.el-table__header) {
    background-color: #f8fafc;

    .dark & {
      background-color: #374151;
    }

    th {
      background-color: #f8fafc !important;
      color: #374151;
      font-weight: 600;

      .dark & {
        background-color: #374151 !important;
        color: #d1d5db;
      }
    }
  }

  :deep(.el-table__body) {
    tr {
      &:hover {
        background-color: #f1f5f9;

        .dark & {
          background-color: #374151;
        }
      }
    }

    td {
      border-bottom: 1px solid #e5e7eb;

      .dark & {
        border-bottom-color: #4b5563;
        color: #d1d5db;
      }
    }
  }
}

// Action buttons styling
.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  align-items: center;
}

// Responsive design
@media (max-width: 1024px) {
  .grid-cols-1.lg\\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .table-card {
    margin-bottom: 1.5rem;
  }
}

// Dark mode support
.dark {
  .text-gray-800 {
    color: #f9fafb;
  }

  .text-green-600 {
    color: #34d399;
  }

  .text-blue-600 {
    color: #60a5fa;
  }

  .text-purple-600 {
    color: #a78bfa;
  }
}
</style>

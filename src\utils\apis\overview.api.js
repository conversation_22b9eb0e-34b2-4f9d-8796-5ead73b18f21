import apiAxios from '@/utils/configs/axios.config.js'

const overviewApi = {

  getOverview() {
    return apiAxios({
      method: 'get',
      url: 'statistics/overview',
    })
  },


  getOverviewByDate(timePeriod, type) {
    return apiAxios({
      method: 'get',
      url: 'statistics/overview/by-date',
      params: {
        time_period: timePeriod,
        type: type,
      },
    })
  },

 
  getDailyStatistics(params = {}) {
    // Clean empty params
    const cleanParams = Object.fromEntries(
      Object.entries(params).filter(([_, value]) => value !== '' && value !== null && value !== undefined)
    )

    return apiAxios({
      method: 'get',
      url: 'statistics/overview/daily',
      params: cleanParams,
    })
  },


  getRevenueStatistics(params = {}) {
    const cleanParams = Object.fromEntries(
      Object.entries(params).filter(([_, value]) => value !== '' && value !== null && value !== undefined)
    )

    return apiAxios({
      method: 'get',
      url: 'statistics/overview/revenue',
      params: cleanParams,
    })
  },
}

export default overviewApi

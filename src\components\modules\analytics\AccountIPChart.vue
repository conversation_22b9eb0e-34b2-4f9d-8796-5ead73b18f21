<template>
  <div class="account-ip-chart">
    <!-- Header v<PERSON><PERSON> bộ lọc -->
    <div class="chart-header mb-6">
        <div class="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
        <!-- Thống kê tổng quan -->
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="summary-item">
            <span class="summary-label">Số lượng tài khoản đăng ký:</span>
            <span class="summary-value text-blue-600">{{ totalSummary.total_registered_accounts || 0 }}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Số lượng IP đăng ký:</span>
            <span class="summary-value text-green-600">{{ totalSummary.total_registered_ips || 0 }}</span>
          </div>
        </div>

        <!-- <PERSON><PERSON> lọc thời gian -->
        <div class="flex items-center gap-3 w-full md:w-auto">
          <!-- Date Range Picker với Quick Options -->
          <div class="flex items-center gap-2 w-full md:w-auto">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="đến"
              start-placeholder="Từ ngày"
              end-placeholder="Đến ngày"
              format="DD/MM/YYYY"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
              @clear="handleDateRangeClear"
              size="small"
              class="date-picker-mobile"
              :shortcuts="dateShortcuts"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Biểu đồ -->
    <div class="chart-container">
      <div v-if="loading" class="loading-container">
        <el-loading text="Đang tải dữ liệu..." />
      </div>
      <div ref="chartRef" class="chart-canvas" v-show="!loading"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { useStatistics } from '@/composables/modules/analytics/index.js'

// Props
const props = defineProps({
  height: {
    type: String,
    default: '400px'
  }
})

// Use statistics composable
const { 
  loading, 
  fetchDailyStatistics, 
  formatDateForAPI, 
  formatChartDataFromAPI,
  getDateRangePresets 
} = useStatistics()

// Reactive data
const chartRef = ref(null)
const chartInstance = ref(null)
const dateRange = ref([])

// Chart data
const chartData = ref({
  dates: [],
  accounts: [],
  ips: [],
  cloneRatio: []
})

// Total summary data
const totalSummary = ref({
  total_registered_accounts: 0,
  total_registered_ips: 0
})

// Date shortcuts for quick selection
const dateShortcuts = getDateRangePresets()

// Initialize date range (15 days by default)
const initializeDateRange = () => {
  const end = new Date()
  const start = new Date()
  start.setDate(start.getDate() - 15)
  
  dateRange.value = [
    start.toISOString().split('T')[0],
    end.toISOString().split('T')[0]
  ]
}

// Handle date range change
const handleDateRangeChange = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    loadChartData()
  }
}

// Handle date range clear
const handleDateRangeClear = () => {
  // Reset to default 15 days when clearing
  initializeDateRange()
  loadChartData()
}

// Load chart data
const loadChartData = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) return
  
  try {
    const [startDate, endDate] = dateRange.value
    
    // Call API to get daily statistics
    const result = await fetchDailyStatistics({
      start_date: formatDateForAPI(startDate),
      end_date: formatDateForAPI(endDate)
    })
    
    if (result.success && result.data) {
      // Format the API data for the chart
      chartData.value = formatChartDataFromAPI(result.data)

      // Update total summary
      if (result.data.total_summary) {
        totalSummary.value = result.data.total_summary
      }

      updateChart()
    } else {
      // Set empty data on failure
      chartData.value = {
        dates: [],
        accounts: [],
        ips: [],
        cloneRatio: []
      }
      updateChart()
      ElMessage.error(result.message || 'Lỗi khi tải dữ liệu biểu đồ')
    }
    
  } catch (error) {
    console.error('Error loading chart data:', error)
    // Set empty data on error
    chartData.value = {
      dates: [],
      accounts: [],
      ips: [],
      cloneRatio: []
    }
    updateChart()
    ElMessage.error('Lỗi khi tải dữ liệu biểu đồ')
  }
}

// Initialize chart
const initChart = () => {
  if (!chartRef.value) return

  chartInstance.value = echarts.init(chartRef.value)
  console.log('Chart instance created:', chartInstance.value)
  
  // Handle empty data case
  if (!chartData.value.dates.length) {
    const isMobile = window.innerWidth < 768
    const isSmallMobile = window.innerWidth < 480
    
    const option = {
      title: {
        text: isMobile ? 'Thống kê TK & IP' : 'Thống kê dữ liệu Tài khoản và IP',
        left: 'center',
        top: isMobile ? 5 : 10,
        textStyle: {
          fontSize: isSmallMobile ? 12 : isMobile ? 14 : 16,
          fontWeight: 'bold',
          fontFamily: 'GitLab Sans, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, sans-serif'
        }
      },
      graphic: {
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: 'Không có dữ liệu để hiển thị',
          fontSize: isSmallMobile ? 12 : isMobile ? 14 : 16,
          fill: '#999',
          fontFamily: 'GitLab Sans, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, sans-serif'
        }
      }
    }
    chartInstance.value.setOption(option)
    return
  }
  
  // Calculate max values for proper scaling
  const maxAccounts = Math.max(...chartData.value.accounts, 0)
  const maxIPs = Math.max(...chartData.value.ips, 0)
  const maxCloneRatio = Math.max(...chartData.value.cloneRatio, 0)
  
  // Calculate proper intervals for Y axes
  const leftMax = Math.max(maxAccounts, maxIPs)
  const leftInterval = leftMax > 0 ? Math.ceil(leftMax / 5) : 3
  const leftAxisMax = leftInterval * 5
  
  const rightInterval = maxCloneRatio > 0 ? Math.ceil(maxCloneRatio / 5 * 100) / 100 : 0.3
  const rightAxisMax = rightInterval * 5
  
  // Responsive title configuration
  const isMobile = window.innerWidth < 768
  const isSmallMobile = window.innerWidth < 480
  
  const option = {
    title: {
      text: isMobile ? 'Thống kê TK & IP' : 'Thống kê dữ liệu Tài khoản và IP',
      left: 'center',
      top: isMobile ? 5 : 10,
      textStyle: {
        fontSize: isSmallMobile ? 12 : isMobile ? 14 : 16,
        fontWeight: 'bold',
        fontFamily: 'GitLab Sans, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, sans-serif',
        overflow: 'break'
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333',
        fontSize: 14
      },
      padding: [10, 15],
      extraCssText: 'box-shadow: 0 2px 8px rgba(0,0,0,0.15); border-radius: 4px;',
      formatter: function(params) {
        console.log('Tooltip formatter called:', params);
        if (!params || params.length === 0) return '';

        const date = params[0].axisValue;
        let accountValue = 0;
        let ipValue = 0;
        let cloneValue = 0;

        // Get all values for this date
        params.forEach(param => {
          const seriesName = param.seriesName;
          if (seriesName === 'Tài khoản' || seriesName === 'TK') {
            accountValue = param.value || 0;
          } else if (seriesName === 'IP') {
            ipValue = param.value || 0;
          } else if (seriesName === 'Hệ số Clone' || seriesName === 'Clone') {
            cloneValue = param.value || 0;
          }
        });

        return `
          <div style="font-weight: bold; margin-bottom: 8px; color: #333;">${date}</div>
          <div style="margin: 4px 0; display: flex; align-items: center;">
            <span style="display: inline-block; width: 12px; height: 12px; background: #fce7f3; margin-right: 8px; border-radius: 2px;"></span>
            Tài khoản: <strong>${accountValue}</strong>
          </div>
          <div style="margin: 4px 0; display: flex; align-items: center;">
            <span style="display: inline-block; width: 12px; height: 12px; background: #14b8a6; margin-right: 8px; border-radius: 2px;"></span>
            IP: <strong>${ipValue}</strong>
          </div>
          <div style="margin: 4px 0; display: flex; align-items: center;">
            <span style="display: inline-block; width: 12px; height: 12px; background: #8b5cf6; margin-right: 8px; border-radius: 2px;"></span>
            Clone: <strong>${cloneValue ? cloneValue.toFixed(2) : '0.00'}</strong>
          </div>
        `;
      }
    },
    legend: {
      data: isMobile ? ['TK', 'IP', 'Clone'] : ['Tài khoản', 'IP', 'Hệ số Clone'],
      top: isMobile ? 35 : 50,
      itemGap: isMobile ? 8 : 10,
      textStyle: {
        fontSize: isSmallMobile ? 10 : isMobile ? 11 : 12
      }
    },
    grid: {
      left: isMobile ? '10%' : '8%',
      right: isMobile ? '10%' : '8%',
      bottom: '3%',
      top: isMobile ? '25%' : '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: chartData.value.dates,
      axisLabel: {
        rotate: 0, // Đặt về 0 để hiển thị ngang
        fontSize: isSmallMobile ? 9 : isMobile ? 10 : 11,
        interval: isMobile ? 'auto' : 0,
        margin: isMobile ? 6 : 8
      }
    },
    yAxis: [
      {
        type: 'value',
        name: isMobile ? 'Số' : 'Số lượng',
        position: 'left',
        max: leftAxisMax,
        interval: leftInterval,
        nameTextStyle: {
          fontSize: isSmallMobile ? 9 : isMobile ? 10 : 12
        },
        axisLabel: {
          formatter: '{value}',
          fontSize: isSmallMobile ? 9 : isMobile ? 10 : 11
        }
      },
      {
        type: 'value',
        name: isMobile ? 'Clone' : 'Hệ số Clone',
        position: 'right',
        max: rightAxisMax,
        interval: rightInterval,
        nameTextStyle: {
          fontSize: isSmallMobile ? 9 : isMobile ? 10 : 12
        },
        axisLabel: {
          formatter: '{value}',
          fontSize: isSmallMobile ? 9 : isMobile ? 10 : 11
        }
      }
    ],
    series: [
      {
        name: isMobile ? 'TK' : 'Tài khoản',
        type: 'bar',
        data: chartData.value.accounts,
        barMaxWidth: isMobile ? 40 : 80,
        barCategoryGap: '20%',
        itemStyle: {
          color: '#fce7f3' // Hồng nhạt thay vì hồng đậm
        },
        yAxisIndex: 0
      },
      {
        name: 'IP',
        type: 'line',
        data: chartData.value.ips,
        itemStyle: {
          color: '#14b8a6'
        },
        lineStyle: {
          color: '#14b8a6',
          width: isMobile ? 1 : 2
        },
        symbol: 'circle',
        symbolSize: isMobile ? 4 : 6,
        yAxisIndex: 0
      },
      {
        name: isMobile ? 'Clone' : 'Hệ số Clone',
        type: 'line',
        data: chartData.value.cloneRatio,
        itemStyle: {
          color: '#8b5cf6'
        },
        lineStyle: {
          type: 'dashed',
          color: '#8b5cf6',
          width: isMobile ? 1 : 2
        },
        symbol: 'diamond',
        symbolSize: isMobile ? 6 : 8,
        yAxisIndex: 1
      }
    ]
  }

  chartInstance.value.setOption(option)

  // Debug: Test if chart is interactive
  console.log('Chart initialized with tooltip:', option.tooltip)

  // Test tooltip manually
  chartInstance.value.on('mouseover', function(params) {
    console.log('Chart mouseover:', params);
  });

  // Force tooltip to show
  chartInstance.value.on('mousemove', function(params) {
    if (params.componentType === 'series') {
      console.log('Series mousemove:', params);
    }
  });
}

// Update chart
const updateChart = () => {
  if (!chartInstance.value) return
  
  // Calculate max values for proper scaling
  const maxAccounts = Math.max(...chartData.value.accounts, 0)
  const maxIPs = Math.max(...chartData.value.ips, 0)
  const maxCloneRatio = Math.max(...chartData.value.cloneRatio, 0)
  
  // Calculate proper intervals for Y axes
  const leftMax = Math.max(maxAccounts, maxIPs)
  const leftInterval = leftMax > 0 ? Math.ceil(leftMax / 5) : 3
  const leftAxisMax = leftInterval * 5
  
  const rightInterval = maxCloneRatio > 0 ? Math.ceil(maxCloneRatio / 5 * 100) / 100 : 0.3
  const rightAxisMax = rightInterval * 5
  
  const option = {
    xAxis: {
      data: chartData.value.dates,
      boundaryGap: true
    },
    yAxis: [
      {
        max: leftAxisMax,
        interval: leftInterval,
      },
      {
        max: rightAxisMax,
        interval: rightInterval,
      }
    ],
    series: [
      {
        data: chartData.value.accounts
      },
      {
        data: chartData.value.ips
      },
      {
        data: chartData.value.cloneRatio
      }
    ]
  }
  
  chartInstance.value.setOption(option)
}

// Handle window resize
const handleResize = () => {
  if (chartInstance.value && chartInstance.value.getDom()) {
    try {
      chartInstance.value.resize()
    } catch (error) {
      console.warn('Chart resize error:', error)
      // Re-initialize chart if resize fails
      setTimeout(() => {
        if (chartData.value.dates.length > 0) {
          initChart()
        }
      }, 100)
    }
  }
}

// Lifecycle
onMounted(() => {
  initializeDateRange()
  
  // Add resize listener
  window.addEventListener('resize', handleResize)
  
  // Load data after a short delay to ensure DOM is ready
  setTimeout(() => {
    loadChartData()
  }, 100)
})

onUnmounted(() => {
  // Remove resize listener first
  window.removeEventListener('resize', handleResize)
  
  // Dispose chart instance
  if (chartInstance.value) {
    try {
      chartInstance.value.dispose()
    } catch (error) {
      console.warn('Chart dispose error:', error)
    }
    chartInstance.value = null
  }
})

// Watch for chart data changes
watch(chartData, () => {
  if (chartInstance.value && chartInstance.value.getDom()) {
    updateChart()
  } else {
    // Initialize chart after data is ready
    setTimeout(() => {
      if (chartRef.value && chartData.value.dates.length > 0) {
        initChart()
      }
    }, 50)
  }
}, { deep: true })
</script>

<style lang="scss" scoped>
.account-ip-chart {
  padding: 0.75rem;
  background: white;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  
  // Mobile responsive
  @media (min-width: 768px) {
    padding: 1rem;
  }
  
  // Dark mode support
  .dark & {
    background-color: #1f2937;
    border-color: #374151;
  }
}

.chart-header {
  .text-gray-800 {
    .dark & {
      color: #f9fafb;
    }
  }
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;

  @media (min-width: 640px) {
    flex-direction: row;
    align-items: center;
    gap: 0.5rem;
  }
}

.summary-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;

  .dark & {
    color: #9ca3af;
  }
}

.summary-value {
  font-size: 1.125rem;
  font-weight: 700;

  @media (min-width: 640px) {
    font-size: 1.25rem;
  }
}

.chart-container {
  padding: 0 5px;
  position: relative;
  
  // Mobile responsive
  @media (min-width: 480px) {
    padding: 0 8px;
  }
  
  @media (min-width: 768px) {
    padding: 0 20px;
  }
  
  .loading-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    z-index: 10;
  }
  
  .chart-canvas {
    width: 100%;
    height: v-bind(height);
    min-height: 280px; // Reduced minimum height for mobile
    
    @media (min-width: 480px) {
      min-height: 300px;
    }
    
    @media (min-width: 768px) {
      min-height: 350px;
    }
  }
}

// El-date-picker styling with mobile responsive
.date-picker-mobile {
  width: 100% !important;
  min-width: 180px;
  max-width: 100%;
  
  @media (min-width: 480px) {
    min-width: 200px;
    max-width: 280px;
  }
  
  @media (min-width: 640px) {
    max-width: 280px;
  }
  
  @media (min-width: 768px) {
    width: 240px !important;
    max-width: 240px;
  }
}

:deep(.el-date-editor) {
  width: 100% !important;
  max-width: 100% !important;
  
  .el-input__wrapper {
    width: 100% !important;
    border-radius: 6px;
    border: 1px solid #d1d5db;
    
    &:hover {
      border-color: #9ca3af;
    }
    
    &.is-focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
  
  // Mobile responsive text size
  .el-input__inner {
    font-size: 13px;
    width: 100% !important;
    
    @media (max-width: 479px) {
      font-size: 11px;
    }
    
    @media (max-width: 767px) {
      font-size: 12px;
    }
  }
  
  // Prevent overflow on mobile
  .el-range-separator {
    @media (max-width: 479px) {
      font-size: 10px;
      padding: 0 1px;
    }
    
    @media (max-width: 640px) {
      font-size: 11px;
      padding: 0 2px;
    }
  }
  
  .el-range-input {
    @media (max-width: 479px) {
      font-size: 11px;
    }
    
    @media (max-width: 640px) {
      font-size: 12px;
    }
  }
}

// Mobile responsive shortcuts popover
:deep(.el-picker-panel__shortcut) {
  @media (max-width: 479px) {
    font-size: 11px;
    padding: 6px 8px;
  }
  
  @media (max-width: 767px) {
    font-size: 12px;
    padding: 8px 12px;
  }
}

// Additional mobile responsiveness
:deep(.el-popper) {
  @media (max-width: 479px) {
    max-width: 90vw !important;
  }
}

// Tooltip responsive styling
:deep(.echarts-tooltip) {
  z-index: 9999 !important;
  pointer-events: none !important;

  @media (max-width: 479px) {
    max-width: 180px !important;
    font-size: 11px !important;
  }

  @media (max-width: 767px) {
    max-width: 220px !important;
    font-size: 12px !important;
  }
}

// Ensure tooltip container is visible
.chart-canvas {
  position: relative;
  z-index: 1;
}

// ECharts tooltip global styles
:global(.echarts-tooltip) {
  z-index: 9999 !important;
  pointer-events: none !important;
}
</style> 